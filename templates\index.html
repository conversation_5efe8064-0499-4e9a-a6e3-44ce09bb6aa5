<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图片翻译器 - ImageTranslator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="{{ url_for('static', filename='css/style.css') }}" rel="stylesheet">
</head>
<body>
    <div class="container-fluid">
        <!-- 导航栏 -->
        <nav class="navbar navbar-expand-lg navbar-dark bg-primary mb-4">
            <div class="container">
                <a class="navbar-brand" href="#">
                    <i class="fas fa-language me-2"></i>
                    图片翻译器
                </a>
                <div class="navbar-text">
                    <small>支持多种语言的图片文字识别与翻译</small>
                </div>
            </div>
        </nav>

        <div class="container">
            <div class="row">
                <!-- 左侧：上传和设置面板 -->
                <div class="col-lg-4 mb-4">
                    <div class="card shadow">
                        <div class="card-header bg-light">
                            <h5 class="card-title mb-0">
                                <i class="fas fa-upload me-2"></i>
                                上传图片
                            </h5>
                        </div>
                        <div class="card-body">
                            <form id="uploadForm" enctype="multipart/form-data">
                                <!-- 文件上传区域 -->
                                <div class="mb-3">
                                    <label for="fileInput" class="form-label">选择图片文件</label>
                                    <input type="file" class="form-control" id="fileInput" name="file" 
                                           accept="image/*" required>
                                    <div class="form-text">支持 PNG, JPG, JPEG, GIF, BMP, WebP 格式</div>
                                </div>

                                <!-- 预览区域 -->
                                <div id="imagePreview" class="mb-3" style="display: none;">
                                    <img id="previewImg" class="img-fluid rounded" style="max-height: 200px;">
                                </div>

                                <!-- 语言设置 -->
                                <div class="row mb-3">
                                    <div class="col-6">
                                        <label for="srcLang" class="form-label">源语言</label>
                                        <select class="form-select" id="srcLang" name="src_lang">
                                            {% for code, name in languages.items() %}
                                            <option value="{{ code }}" {% if code == 'eng' %}selected{% endif %}>
                                                {{ name }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                    <div class="col-6">
                                        <label for="destLang" class="form-label">目标语言</label>
                                        <select class="form-select" id="destLang" name="dest_lang">
                                            {% for code, name in languages.items() %}
                                            <option value="{{ code }}" {% if code == 'chi_sim' %}selected{% endif %}>
                                                {{ name }}
                                            </option>
                                            {% endfor %}
                                        </select>
                                    </div>
                                </div>

                                <!-- 翻译器选择 -->
                                <div class="mb-3">
                                    <label for="translator" class="form-label">翻译引擎</label>
                                    <select class="form-select" id="translator" name="translator">
                                        {% for code, name in translators.items() %}
                                        <option value="{{ code }}" {% if code == 'google' %}selected{% endif %}>
                                            {{ name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                    <div class="form-text">
                                        <small class="text-warning">
                                            <i class="fas fa-exclamation-triangle"></i>
                                            注意：由于兼容性问题，目前只支持 Google 翻译
                                        </small>
                                    </div>
                                </div>

                                <!-- OCR 选择 -->
                                <div class="mb-3">
                                    <label for="ocr" class="form-label">文字识别引擎</label>
                                    <select class="form-select" id="ocr" name="ocr">
                                        {% for code, name in ocr_options.items() %}
                                        <option value="{{ code }}" {% if code == 'tesseract' %}selected{% endif %}>
                                            {{ name }}
                                        </option>
                                        {% endfor %}
                                    </select>
                                </div>

                                <!-- 提交按钮 -->
                                <button type="submit" class="btn btn-primary w-100" id="translateBtn">
                                    <i class="fas fa-magic me-2"></i>
                                    开始翻译
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- 右侧：结果显示区域 -->
                <div class="col-lg-8">
                    <!-- 加载状态 -->
                    <div id="loadingArea" class="text-center py-5" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">处理中...</span>
                        </div>
                        <div class="mt-3">
                            <h5>正在处理图片...</h5>
                            <p class="text-muted">这可能需要几秒钟时间，请耐心等待</p>
                        </div>
                    </div>

                    <!-- 结果区域 -->
                    <div id="resultArea" style="display: none;">
                        <div class="card shadow">
                            <div class="card-header bg-success text-white">
                                <h5 class="card-title mb-0">
                                    <i class="fas fa-check-circle me-2"></i>
                                    翻译结果
                                </h5>
                            </div>
                            <div class="card-body">
                                <!-- 翻译后的图片 -->
                                <div class="mb-4">
                                    <h6>翻译后的图片：</h6>
                                    <img id="resultImage" class="img-fluid rounded border" style="max-width: 100%;">
                                </div>

                                <!-- 文本信息 -->
                                <div id="textInfo">
                                    <h6>识别的文本：</h6>
                                    <div id="textList" class="list-group">
                                        <!-- 动态生成的文本列表 -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 错误信息 -->
                    <div id="errorArea" class="alert alert-danger" style="display: none;">
                        <h5><i class="fas fa-exclamation-triangle me-2"></i>处理失败</h5>
                        <p id="errorMessage"></p>
                    </div>

                    <!-- 欢迎信息 -->
                    <div id="welcomeArea" class="text-center py-5">
                        <div class="mb-4">
                            <i class="fas fa-image fa-5x text-muted"></i>
                        </div>
                        <h3 class="text-muted">欢迎使用图片翻译器</h3>
                        <p class="text-muted">
                            上传一张包含文字的图片，我们将为您识别并翻译其中的文字内容。<br>
                            支持多种语言识别和翻译，让语言不再是障碍！
                        </p>
                        <div class="mt-4">
                            <h6 class="text-muted">功能特点：</h6>
                            <div class="row mt-3">
                                <div class="col-md-4">
                                    <i class="fas fa-eye fa-2x text-primary mb-2"></i>
                                    <p><small>智能文字识别</small></p>
                                </div>
                                <div class="col-md-4">
                                    <i class="fas fa-language fa-2x text-success mb-2"></i>
                                    <p><small>多语言翻译</small></p>
                                </div>
                                <div class="col-md-4">
                                    <i class="fas fa-magic fa-2x text-warning mb-2"></i>
                                    <p><small>图片重新生成</small></p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <!-- 自定义 JS -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
