#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版图片翻译器
避免兼容性问题，只使用基本功能
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import pytesseract
from googletrans import Translator
import logging

logger = logging.getLogger(__name__)

class SimpleImageTranslator:
    """简化版图片翻译器"""
    
    def __init__(self, img, src_lang='en', dest_lang='zh-cn'):
        """
        初始化翻译器
        img: numpy 数组格式的图片
        src_lang: 源语言代码
        dest_lang: 目标语言代码
        """
        self.img = img
        self.src_lang = src_lang
        self.dest_lang = dest_lang
        self.translator = Translator()
        self.text_regions = []
        
    def detect_text_regions(self):
        """检测文本区域"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(self.img, cv2.COLOR_RGB2GRAY)
            
            # 使用 Tesseract 检测文本区域
            data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT)
            
            text_regions = []
            for i in range(len(data['text'])):
                text = data['text'][i].strip()
                if text and int(data['conf'][i]) > 30:  # 置信度阈值
                    x = data['left'][i]
                    y = data['top'][i]
                    w = data['width'][i]
                    h = data['height'][i]
                    
                    text_regions.append({
                        'text': text,
                        'bbox': (x, y, w, h),
                        'confidence': int(data['conf'][i])
                    })
            
            self.text_regions = text_regions
            return text_regions
            
        except Exception as e:
            logger.error(f"文本检测失败: {e}")
            return []
    
    def translate_text(self, text):
        """翻译文本"""
        try:
            result = self.translator.translate(text, src=self.src_lang, dest=self.dest_lang)
            return result.text
        except Exception as e:
            logger.error(f"翻译失败: {e}")
            return text  # 翻译失败时返回原文
    
    def create_translated_image(self):
        """创建翻译后的图片"""
        try:
            # 检测文本区域
            if not self.text_regions:
                self.detect_text_regions()
            
            # 复制原图
            result_img = self.img.copy()
            
            # 转换为 PIL 图片以便绘制文本
            pil_img = Image.fromarray(result_img)
            draw = ImageDraw.Draw(pil_img)
            
            # 尝试加载字体
            try:
                # 使用项目中的字体文件
                font = ImageFont.truetype('font/Cantarell.ttf', size=20)
            except:
                try:
                    # 尝试使用系统字体
                    font = ImageFont.truetype('arial.ttf', size=20)
                except:
                    # 使用默认字体
                    font = ImageFont.load_default()
            
            translated_texts = []
            
            for region in self.text_regions:
                text = region['text']
                bbox = region['bbox']
                x, y, w, h = bbox
                
                # 翻译文本
                translated_text = self.translate_text(text)
                translated_texts.append({
                    'original': text,
                    'translated': translated_text,
                    'bbox': bbox
                })
                
                # 用白色矩形覆盖原文
                draw.rectangle([x-2, y-2, x+w+2, y+h+2], fill='white')
                
                # 绘制翻译后的文本
                draw.text((x, y), translated_text, fill='black', font=font)
            
            # 转换回 numpy 数组
            result_array = np.array(pil_img)
            
            return result_array, translated_texts
            
        except Exception as e:
            logger.error(f"创建翻译图片失败: {e}")
            return self.img, []
    
    def process(self):
        """处理图片翻译的主要方法"""
        try:
            result_img, text_info = self.create_translated_image()
            return {
                'success': True,
                'result_image': result_img,
                'text_info': text_info
            }
        except Exception as e:
            logger.error(f"处理失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

def create_simple_translator(img_array, src_lang='en', dest_lang='zh-cn'):
    """创建简化版翻译器的工厂函数"""
    return SimpleImageTranslator(img_array, src_lang, dest_lang)

if __name__ == "__main__":
    # 测试代码
    print("简化版图片翻译器已加载")
