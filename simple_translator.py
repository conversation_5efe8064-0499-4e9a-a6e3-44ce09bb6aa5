#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版图片翻译器
避免兼容性问题，只使用基本功能
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import pytesseract
from googletrans import Translator
import logging
import re
import os

logger = logging.getLogger(__name__)

class SimpleImageTranslator:
    """简化版图片翻译器"""
    
    def __init__(self, img, src_lang='en', dest_lang='zh-cn'):
        """
        初始化翻译器
        img: numpy 数组格式的图片
        src_lang: 源语言代码
        dest_lang: 目标语言代码
        """
        self.img = img
        self.src_lang = src_lang
        self.dest_lang = dest_lang
        self.translator = Translator()
        self.text_regions = []
        
    def detect_text_regions(self):
        """检测文本区域"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(self.img, cv2.COLOR_RGB2GRAY)

            # 图像预处理 - 提高OCR识别率
            # 1. 高斯模糊去噪
            gray = cv2.GaussianBlur(gray, (3, 3), 0)

            # 2. 自适应阈值二值化
            binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY, 11, 2)

            # 3. 形态学操作，连接文字
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

            # 使用 Tesseract 检测文本区域
            # 尝试多种配置以提高识别率
            configs_to_try = [
                {'config': r'--oem 3 --psm 6', 'lang': 'chi_sim+eng'},
                {'config': r'--oem 3 --psm 8', 'lang': 'chi_sim+eng'},
                {'config': r'--oem 3 --psm 6', 'lang': 'eng'},
                {'config': r'--oem 3 --psm 8', 'lang': 'eng'},
            ]

            data = None
            for config_info in configs_to_try:
                try:
                    # 先尝试用处理后的二值图像
                    data = pytesseract.image_to_data(binary, output_type=pytesseract.Output.DICT,
                                                   config=config_info['config'],
                                                   lang=config_info['lang'])
                    break
                except:
                    try:
                        # 如果失败，使用原始灰度图
                        data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT,
                                                       config=config_info['config'],
                                                       lang=config_info['lang'])
                        break
                    except:
                        continue

            if data is None:
                logger.error("所有OCR配置都失败了")
                return []

            # 按行合并文本
            text_regions = []
            current_line = []
            current_y = -1
            line_threshold = 20  # 行间距阈值

            for i in range(len(data['text'])):
                text = data['text'][i].strip()
                conf = int(data['conf'][i]) if data['conf'][i] != '-1' else 0

                if text and conf > 20:  # 降低置信度阈值
                    x = data['left'][i]
                    y = data['top'][i]
                    w = data['width'][i]
                    h = data['height'][i]

                    # 判断是否是新行
                    if current_y == -1 or abs(y - current_y) > line_threshold:
                        # 保存上一行
                        if current_line:
                            self._save_line_as_region(current_line, text_regions)
                        # 开始新行
                        current_line = [{
                            'text': text,
                            'bbox': (x, y, w, h),
                            'confidence': conf
                        }]
                        current_y = y
                    else:
                        # 同一行，添加到当前行
                        current_line.append({
                            'text': text,
                            'bbox': (x, y, w, h),
                            'confidence': conf
                        })

            # 保存最后一行
            if current_line:
                self._save_line_as_region(current_line, text_regions)

            self.text_regions = text_regions
            return text_regions

        except Exception as e:
            logger.error(f"文本检测失败: {e}")
            return []

    def _save_line_as_region(self, line_words, text_regions):
        """将一行的单词合并为一个文本区域"""
        if not line_words:
            return

        # 合并文本
        full_text = ' '.join([word['text'] for word in line_words])

        # 计算边界框
        min_x = min([word['bbox'][0] for word in line_words])
        min_y = min([word['bbox'][1] for word in line_words])
        max_x = max([word['bbox'][0] + word['bbox'][2] for word in line_words])
        max_y = max([word['bbox'][1] + word['bbox'][3] for word in line_words])

        # 计算平均置信度
        avg_conf = sum([word['confidence'] for word in line_words]) / len(line_words)

        text_regions.append({
            'text': full_text,
            'bbox': (min_x, min_y, max_x - min_x, max_y - min_y),
            'confidence': int(avg_conf)
        })
    
    def translate_text(self, text):
        """翻译文本"""
        # 如果文本太短或只包含特殊字符，跳过翻译
        if len(text.strip()) < 2 or not any(c.isalnum() or ord(c) > 127 for c in text):
            return text

        # 重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 创建新的翻译器实例以避免连接问题
                translator = Translator()
                result = translator.translate(text, src=self.src_lang, dest=self.dest_lang)
                if result and result.text:
                    return result.text
                else:
                    return text
            except Exception as e:
                logger.warning(f"翻译尝试 {attempt + 1} 失败: {e}")
                if attempt == max_retries - 1:
                    logger.error(f"翻译最终失败: {e}")
                    # 尝试简单的词汇替换作为备选方案
                    return self.simple_translate_fallback(text)
                # 等待一下再重试
                import time
                time.sleep(1)

    def simple_translate_fallback(self, text):
        """简单的离线翻译备选方案"""
        # 基本的中英文词汇对照
        translation_dict = {
            # 英文到中文
            'waterproof': '防水',
            'rainproof': '防雨',
            'windproof': '防风',
            'snowproof': '防雪',
            'outdoor': '户外',
            'design': '设计',
            'special': '专用',
            'for': '为',
            'normal': '正常',
            'light': '灯光',
            'lighting': '照明',
            'plastic': '塑料',
            'cup': '杯子',
            'anti': '防',
            'drop': '摔',
            'flame': '阻燃',
            'retardant': '阻燃',
            'copper': '铜',
            'pure': '纯',
            'high': '高',
            'temperature': '温度',
            'resistant': '耐',
            'pvc': 'PVC',
            'pet': 'PET',
            'material': '材料',
            'ue': 'UE',
            'fra': 'FRA',
        }

        # 如果是纯英文且在字典中，进行替换
        if self.src_lang == 'en' and self.dest_lang == 'zh-cn':
            text_lower = text.lower()
            for en_word, zh_word in translation_dict.items():
                if en_word in text_lower:
                    text = re.sub(re.escape(en_word), zh_word, text, flags=re.IGNORECASE)

        return f"[离线翻译] {text}"

    def _load_appropriate_font(self, size=20):
        """加载合适的字体"""
        # 字体文件路径列表，按优先级排序
        font_paths = [
            # 项目字体 - 优先使用新添加的字体
            'font/msyh.ttc',  # 微软雅黑
            'font/msgothic.ttc',  # MS Gothic
            'font/Arial-Unicode-Regular.ttf',  # Arial Unicode
            'font/NotoSansMonoCJK-VF.ttf.ttc',  # Noto Sans CJK
            'font/Cantarell.ttf',
            # Windows 系统字体
            'C:/Windows/Fonts/msyh.ttc',
            'C:/Windows/Fonts/simsun.ttc',
            'C:/Windows/Fonts/arial.ttf',
            'C:/Windows/Fonts/calibri.ttf',
            'C:/Windows/Fonts/tahoma.ttf',
            'C:/Windows/Fonts/verdana.ttf',
            'C:/Windows/Fonts/simhei.ttf',
            # Linux 字体
            '/usr/share/fonts/truetype/dejavu/DejaVuSans.ttf',
            '/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf',
            # macOS 字体
            '/System/Library/Fonts/Arial.ttf',
            '/System/Library/Fonts/Helvetica.ttc',
        ]

        # 尝试加载字体
        for font_path in font_paths:
            try:
                if os.path.exists(font_path):
                    font = ImageFont.truetype(font_path, size=size)
                    logger.info(f"成功加载字体: {font_path}")
                    return font
            except Exception as e:
                logger.debug(f"无法加载字体 {font_path}: {e}")
                continue

        # 如果所有字体都失败，使用默认字体
        logger.warning("无法加载任何TrueType字体，使用默认字体")
        return ImageFont.load_default()
    
    def create_translated_image(self):
        """创建翻译后的图片"""
        try:
            # 检测文本区域
            if not self.text_regions:
                self.detect_text_regions()
            
            # 复制原图
            result_img = self.img.copy()
            
            # 转换为 PIL 图片以便绘制文本
            pil_img = Image.fromarray(result_img)
            draw = ImageDraw.Draw(pil_img)
            
            # 尝试加载字体 - 改进字体加载逻辑
            font = self._load_appropriate_font()
            
            translated_texts = []
            
            for region in self.text_regions:
                text = region['text']
                bbox = region['bbox']
                x, y, w, h = bbox
                
                # 翻译文本
                translated_text = self.translate_text(text)
                translated_texts.append({
                    'original': text,
                    'translated': translated_text,
                    'bbox': bbox
                })

                # 用白色矩形覆盖原文（稍微扩大一点确保完全覆盖）
                padding = 3
                draw.rectangle([x-padding, y-padding, x+w+padding, y+h+padding],
                             fill='white', outline='white')

                # 计算合适的字体大小
                font_size = max(10, min(h-2, 24))  # 字体大小在10-24之间

                # 绘制翻译后的文本
                text_drawn = False
                for attempt in range(3):  # 尝试3种不同的方法
                    try:
                        if attempt == 0:
                            # 尝试1: 使用项目字体
                            font = self._load_appropriate_font(font_size)
                        elif attempt == 1:
                            # 尝试2: 使用更小的字体
                            font = self._load_appropriate_font(max(8, font_size-4))
                        else:
                            # 尝试3: 使用默认字体
                            font = ImageFont.load_default()

                        # 绘制文本
                        draw.text((x, y), translated_text, fill='black', font=font)
                        text_drawn = True
                        logger.debug(f"文本绘制成功 (尝试 {attempt+1}): {translated_text[:20]}...")
                        break

                    except Exception as e:
                        logger.warning(f"文本绘制尝试 {attempt+1} 失败: {e}")
                        continue

                if not text_drawn:
                    logger.error(f"所有文本绘制尝试都失败了: {translated_text}")
                    # 最后的备选方案：绘制简单的占位符
                    try:
                        draw.text((x, y), "[TEXT]", fill='red', font=ImageFont.load_default())
                    except:
                        pass
            
            # 转换回 numpy 数组
            result_array = np.array(pil_img)

            # 验证图片数据
            if result_array is None or result_array.size == 0:
                raise Exception("生成的图片数据为空")

            logger.info(f"生成的图片尺寸: {result_array.shape}")
            logger.info(f"图片数据类型: {result_array.dtype}")
            logger.info(f"图片数据范围: {result_array.min()} - {result_array.max()}")

            # 调试：保存处理后的图片
            try:
                debug_path = 'debug_result.png'
                pil_img.save(debug_path, 'PNG')
                logger.info(f"调试图片已保存: {debug_path}")

                # 验证保存的文件
                if os.path.exists(debug_path):
                    file_size = os.path.getsize(debug_path)
                    logger.info(f"调试图片文件大小: {file_size} 字节")
                else:
                    logger.error("调试图片文件未成功保存")

            except Exception as e:
                logger.error(f"保存调试图片失败: {e}")

            return result_array, translated_texts
            
        except Exception as e:
            logger.error(f"创建翻译图片失败: {e}")
            return self.img, []
    
    def process(self):
        """处理图片翻译的主要方法"""
        try:
            result_img, text_info = self.create_translated_image()
            return {
                'success': True,
                'result_image': result_img,
                'text_info': text_info
            }
        except Exception as e:
            logger.error(f"处理失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

def create_simple_translator(img_array, src_lang='en', dest_lang='zh-cn'):
    """创建简化版翻译器的工厂函数"""
    return SimpleImageTranslator(img_array, src_lang, dest_lang)

if __name__ == "__main__":
    # 测试代码
    print("简化版图片翻译器已加载")
