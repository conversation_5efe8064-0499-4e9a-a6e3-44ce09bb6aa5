#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
简化版图片翻译器
避免兼容性问题，只使用基本功能
"""

import cv2
import numpy as np
from PIL import Image, ImageDraw, ImageFont
import pytesseract
from googletrans import Translator
import logging
import re

logger = logging.getLogger(__name__)

class SimpleImageTranslator:
    """简化版图片翻译器"""
    
    def __init__(self, img, src_lang='en', dest_lang='zh-cn'):
        """
        初始化翻译器
        img: numpy 数组格式的图片
        src_lang: 源语言代码
        dest_lang: 目标语言代码
        """
        self.img = img
        self.src_lang = src_lang
        self.dest_lang = dest_lang
        self.translator = Translator()
        self.text_regions = []
        
    def detect_text_regions(self):
        """检测文本区域"""
        try:
            # 转换为灰度图
            gray = cv2.cvtColor(self.img, cv2.COLOR_RGB2GRAY)

            # 图像预处理 - 提高OCR识别率
            # 1. 高斯模糊去噪
            gray = cv2.GaussianBlur(gray, (3, 3), 0)

            # 2. 自适应阈值二值化
            binary = cv2.adaptiveThreshold(gray, 255, cv2.ADAPTIVE_THRESH_GAUSSIAN_C,
                                         cv2.THRESH_BINARY, 11, 2)

            # 3. 形态学操作，连接文字
            kernel = cv2.getStructuringElement(cv2.MORPH_RECT, (2, 2))
            binary = cv2.morphologyEx(binary, cv2.MORPH_CLOSE, kernel)

            # 使用 Tesseract 检测文本区域
            # 尝试多种配置以提高识别率
            configs_to_try = [
                {'config': r'--oem 3 --psm 6', 'lang': 'chi_sim+eng'},
                {'config': r'--oem 3 --psm 8', 'lang': 'chi_sim+eng'},
                {'config': r'--oem 3 --psm 6', 'lang': 'eng'},
                {'config': r'--oem 3 --psm 8', 'lang': 'eng'},
            ]

            data = None
            for config_info in configs_to_try:
                try:
                    # 先尝试用处理后的二值图像
                    data = pytesseract.image_to_data(binary, output_type=pytesseract.Output.DICT,
                                                   config=config_info['config'],
                                                   lang=config_info['lang'])
                    break
                except:
                    try:
                        # 如果失败，使用原始灰度图
                        data = pytesseract.image_to_data(gray, output_type=pytesseract.Output.DICT,
                                                       config=config_info['config'],
                                                       lang=config_info['lang'])
                        break
                    except:
                        continue

            if data is None:
                logger.error("所有OCR配置都失败了")
                return []

            # 按行合并文本
            text_regions = []
            current_line = []
            current_y = -1
            line_threshold = 20  # 行间距阈值

            for i in range(len(data['text'])):
                text = data['text'][i].strip()
                conf = int(data['conf'][i]) if data['conf'][i] != '-1' else 0

                if text and conf > 20:  # 降低置信度阈值
                    x = data['left'][i]
                    y = data['top'][i]
                    w = data['width'][i]
                    h = data['height'][i]

                    # 判断是否是新行
                    if current_y == -1 or abs(y - current_y) > line_threshold:
                        # 保存上一行
                        if current_line:
                            self._save_line_as_region(current_line, text_regions)
                        # 开始新行
                        current_line = [{
                            'text': text,
                            'bbox': (x, y, w, h),
                            'confidence': conf
                        }]
                        current_y = y
                    else:
                        # 同一行，添加到当前行
                        current_line.append({
                            'text': text,
                            'bbox': (x, y, w, h),
                            'confidence': conf
                        })

            # 保存最后一行
            if current_line:
                self._save_line_as_region(current_line, text_regions)

            self.text_regions = text_regions
            return text_regions

        except Exception as e:
            logger.error(f"文本检测失败: {e}")
            return []

    def _save_line_as_region(self, line_words, text_regions):
        """将一行的单词合并为一个文本区域"""
        if not line_words:
            return

        # 合并文本
        full_text = ' '.join([word['text'] for word in line_words])

        # 计算边界框
        min_x = min([word['bbox'][0] for word in line_words])
        min_y = min([word['bbox'][1] for word in line_words])
        max_x = max([word['bbox'][0] + word['bbox'][2] for word in line_words])
        max_y = max([word['bbox'][1] + word['bbox'][3] for word in line_words])

        # 计算平均置信度
        avg_conf = sum([word['confidence'] for word in line_words]) / len(line_words)

        text_regions.append({
            'text': full_text,
            'bbox': (min_x, min_y, max_x - min_x, max_y - min_y),
            'confidence': int(avg_conf)
        })
    
    def translate_text(self, text):
        """翻译文本"""
        # 如果文本太短或只包含特殊字符，跳过翻译
        if len(text.strip()) < 2 or not any(c.isalnum() or ord(c) > 127 for c in text):
            return text

        # 重试机制
        max_retries = 3
        for attempt in range(max_retries):
            try:
                # 创建新的翻译器实例以避免连接问题
                translator = Translator()
                result = translator.translate(text, src=self.src_lang, dest=self.dest_lang)
                if result and result.text:
                    return result.text
                else:
                    return text
            except Exception as e:
                logger.warning(f"翻译尝试 {attempt + 1} 失败: {e}")
                if attempt == max_retries - 1:
                    logger.error(f"翻译最终失败: {e}")
                    # 尝试简单的词汇替换作为备选方案
                    return self.simple_translate_fallback(text)
                # 等待一下再重试
                import time
                time.sleep(1)

    def simple_translate_fallback(self, text):
        """简单的离线翻译备选方案"""
        # 基本的中英文词汇对照
        translation_dict = {
            # 英文到中文
            'waterproof': '防水',
            'rainproof': '防雨',
            'windproof': '防风',
            'snowproof': '防雪',
            'outdoor': '户外',
            'design': '设计',
            'special': '专用',
            'for': '为',
            'normal': '正常',
            'light': '灯光',
            'lighting': '照明',
            'plastic': '塑料',
            'cup': '杯子',
            'anti': '防',
            'drop': '摔',
            'flame': '阻燃',
            'retardant': '阻燃',
            'copper': '铜',
            'pure': '纯',
            'high': '高',
            'temperature': '温度',
            'resistant': '耐',
            'pvc': 'PVC',
            'pet': 'PET',
            'material': '材料',
            'ue': 'UE',
            'fra': 'FRA',
        }

        # 如果是纯英文且在字典中，进行替换
        if self.src_lang == 'en' and self.dest_lang == 'zh-cn':
            text_lower = text.lower()
            for en_word, zh_word in translation_dict.items():
                if en_word in text_lower:
                    text = re.sub(re.escape(en_word), zh_word, text, flags=re.IGNORECASE)

        return f"[离线翻译] {text}"
    
    def create_translated_image(self):
        """创建翻译后的图片"""
        try:
            # 检测文本区域
            if not self.text_regions:
                self.detect_text_regions()
            
            # 复制原图
            result_img = self.img.copy()
            
            # 转换为 PIL 图片以便绘制文本
            pil_img = Image.fromarray(result_img)
            draw = ImageDraw.Draw(pil_img)
            
            # 尝试加载字体
            try:
                # 使用项目中的字体文件
                font = ImageFont.truetype('font/Cantarell.ttf', size=20)
            except:
                try:
                    # 尝试使用系统字体
                    font = ImageFont.truetype('arial.ttf', size=20)
                except:
                    # 使用默认字体
                    font = ImageFont.load_default()
            
            translated_texts = []
            
            for region in self.text_regions:
                text = region['text']
                bbox = region['bbox']
                x, y, w, h = bbox
                
                # 翻译文本
                translated_text = self.translate_text(text)
                translated_texts.append({
                    'original': text,
                    'translated': translated_text,
                    'bbox': bbox
                })
                
                # 用白色矩形覆盖原文
                draw.rectangle([x-2, y-2, x+w+2, y+h+2], fill='white')
                
                # 绘制翻译后的文本
                draw.text((x, y), translated_text, fill='black', font=font)
            
            # 转换回 numpy 数组
            result_array = np.array(pil_img)
            
            return result_array, translated_texts
            
        except Exception as e:
            logger.error(f"创建翻译图片失败: {e}")
            return self.img, []
    
    def process(self):
        """处理图片翻译的主要方法"""
        try:
            result_img, text_info = self.create_translated_image()
            return {
                'success': True,
                'result_image': result_img,
                'text_info': text_info
            }
        except Exception as e:
            logger.error(f"处理失败: {e}")
            return {
                'success': False,
                'error': str(e)
            }

def create_simple_translator(img_array, src_lang='en', dest_lang='zh-cn'):
    """创建简化版翻译器的工厂函数"""
    return SimpleImageTranslator(img_array, src_lang, dest_lang)

if __name__ == "__main__":
    # 测试代码
    print("简化版图片翻译器已加载")
