#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
检查生成的图片文件
"""

import os
from PIL import Image
import cv2
import numpy as np

def check_image_file(filename):
    """检查图片文件"""
    print(f"\n=== 检查图片: {filename} ===")
    
    if not os.path.exists(filename):
        print(f"❌ 文件不存在: {filename}")
        return False
    
    file_size = os.path.getsize(filename)
    print(f"📁 文件大小: {file_size} 字节")
    
    try:
        # 使用 PIL 打开图片
        with Image.open(filename) as img:
            print(f"🖼️  PIL 信息:")
            print(f"   尺寸: {img.size}")
            print(f"   模式: {img.mode}")
            print(f"   格式: {img.format}")
            
        # 使用 OpenCV 打开图片
        cv_img = cv2.imread(filename)
        if cv_img is not None:
            print(f"🔍 OpenCV 信息:")
            print(f"   形状: {cv_img.shape}")
            print(f"   数据类型: {cv_img.dtype}")
            print(f"   数据范围: {cv_img.min()} - {cv_img.max()}")
        else:
            print("❌ OpenCV 无法读取图片")
            
        print("✅ 图片文件正常")
        return True
        
    except Exception as e:
        print(f"❌ 图片文件损坏: {e}")
        return False

def main():
    """主函数"""
    print("🔍 检查图片翻译结果文件")
    
    # 要检查的图片文件列表
    image_files = [
        'test_image.png',
        'test_result.png', 
        'debug_result.png',
        'web_test_result.png'
    ]
    
    valid_count = 0
    for filename in image_files:
        if check_image_file(filename):
            valid_count += 1
    
    print(f"\n📊 总结: {valid_count}/{len(image_files)} 个图片文件正常")
    
    if valid_count == len(image_files):
        print("🎉 所有图片文件都正常！")
    else:
        print("⚠️  部分图片文件有问题")

if __name__ == "__main__":
    main()
