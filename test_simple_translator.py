#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试简化版翻译器
"""

import cv2
import numpy as np
from simple_translator import create_simple_translator

def test_translator():
    """测试翻译器功能"""
    
    # 读取测试图片
    try:
        img = cv2.imread('test_image.png')
        if img is None:
            print("无法读取测试图片，请先运行 create_test_image.py")
            return
        
        # 转换颜色空间
        img_rgb = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        print("开始测试翻译器...")
        
        # 创建翻译器
        translator = create_simple_translator(img_rgb, 'en', 'zh-cn')
        
        # 执行翻译
        result = translator.process()
        
        if result['success']:
            print("翻译成功！")
            print(f"检测到 {len(result['text_info'])} 个文本区域")
            
            for i, text_info in enumerate(result['text_info']):
                print(f"文本 {i+1}:")
                print(f"  原文: {text_info['original']}")
                print(f"  译文: {text_info['translated']}")
                print()
            
            # 保存结果图片
            result_img = result['result_image']
            result_bgr = cv2.cvtColor(result_img, cv2.COLOR_RGB2BGR)
            cv2.imwrite('test_result.png', result_bgr)
            print("结果图片已保存为: test_result.png")
            
        else:
            print(f"翻译失败: {result['error']}")
            
    except Exception as e:
        print(f"测试过程中发生错误: {e}")

if __name__ == "__main__":
    test_translator()
