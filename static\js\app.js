// 图片翻译器 JavaScript 功能

document.addEventListener('DOMContentLoaded', function() {
    // 获取DOM元素
    const uploadForm = document.getElementById('uploadForm');
    const fileInput = document.getElementById('fileInput');
    const imagePreview = document.getElementById('imagePreview');
    const previewImg = document.getElementById('previewImg');
    const translateBtn = document.getElementById('translateBtn');
    
    const welcomeArea = document.getElementById('welcomeArea');
    const loadingArea = document.getElementById('loadingArea');
    const resultArea = document.getElementById('resultArea');
    const errorArea = document.getElementById('errorArea');
    
    const resultImage = document.getElementById('resultImage');
    const textList = document.getElementById('textList');
    const errorMessage = document.getElementById('errorMessage');

    // 文件选择事件
    fileInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            // 验证文件类型
            const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/gif', 'image/bmp', 'image/webp'];
            if (!allowedTypes.includes(file.type)) {
                showError('请选择有效的图片文件（PNG, JPG, JPEG, GIF, BMP, WebP）');
                return;
            }

            // 验证文件大小（16MB）
            if (file.size > 16 * 1024 * 1024) {
                showError('文件大小不能超过 16MB');
                return;
            }

            // 显示图片预览
            const reader = new FileReader();
            reader.onload = function(e) {
                previewImg.src = e.target.result;
                imagePreview.style.display = 'block';
                imagePreview.classList.add('fade-in');
            };
            reader.readAsDataURL(file);
        } else {
            imagePreview.style.display = 'none';
        }
    });

    // 表单提交事件
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        // 验证表单
        if (!fileInput.files[0]) {
            showError('请先选择一张图片');
            return;
        }

        // 显示加载状态
        showLoading();
        
        // 创建FormData对象
        const formData = new FormData(uploadForm);
        
        // 发送请求
        fetch('/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showResult(data);
            } else {
                showError(data.error || '翻译失败，请重试');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showError('网络错误，请检查连接后重试');
        });
    });

    // 显示加载状态
    function showLoading() {
        hideAllAreas();
        loadingArea.style.display = 'block';
        loadingArea.classList.add('fade-in');
        translateBtn.disabled = true;
        translateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>处理中...';
    }

    // 显示结果
    function showResult(data) {
        hideAllAreas();
        
        // 显示翻译后的图片
        resultImage.src = 'data:image/png;base64,' + data.result_image;
        
        // 显示文本信息
        displayTextInfo(data.text_info);
        
        // 显示结果区域
        resultArea.style.display = 'block';
        resultArea.classList.add('fade-in');
        
        // 重置按钮状态
        resetButton();
        
        // 滚动到结果区域
        resultArea.scrollIntoView({ behavior: 'smooth' });
    }

    // 显示错误信息
    function showError(message) {
        hideAllAreas();
        errorMessage.textContent = message;
        errorArea.style.display = 'block';
        errorArea.classList.add('fade-in');
        resetButton();
    }

    // 隐藏所有区域
    function hideAllAreas() {
        welcomeArea.style.display = 'none';
        loadingArea.style.display = 'none';
        resultArea.style.display = 'none';
        errorArea.style.display = 'none';
    }

    // 重置按钮状态
    function resetButton() {
        translateBtn.disabled = false;
        translateBtn.innerHTML = '<i class="fas fa-magic me-2"></i>开始翻译';
    }

    // 显示文本信息
    function displayTextInfo(textInfo) {
        textList.innerHTML = '';
        
        if (textInfo.length === 0) {
            textList.innerHTML = '<div class="alert alert-info">未检测到文本内容</div>';
            return;
        }

        textInfo.forEach((item, index) => {
            const listItem = document.createElement('div');
            listItem.className = 'list-group-item';
            listItem.innerHTML = `
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <h6 class="mb-2">文本片段 ${index + 1}</h6>
                        <div class="text-original">
                            <strong>原文：</strong>${escapeHtml(item.original)}
                        </div>
                        <div class="text-translated">
                            <strong>译文：</strong>${escapeHtml(item.translated)}
                        </div>
                    </div>
                    <button class="btn btn-sm btn-outline-primary ms-2" onclick="copyText('${escapeHtml(item.translated)}')">
                        <i class="fas fa-copy"></i>
                    </button>
                </div>
            `;
            textList.appendChild(listItem);
        });
    }

    // HTML转义函数
    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    // 复制文本到剪贴板
    window.copyText = function(text) {
        navigator.clipboard.writeText(text).then(function() {
            // 显示复制成功提示
            showToast('文本已复制到剪贴板');
        }).catch(function(err) {
            console.error('复制失败:', err);
            showToast('复制失败，请手动选择文本');
        });
    };

    // 显示提示消息
    function showToast(message) {
        // 创建提示元素
        const toast = document.createElement('div');
        toast.className = 'toast-message';
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: #28a745;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 9999;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            animation: slideIn 0.3s ease-out;
        `;
        
        // 添加动画样式
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }
            @keyframes slideOut {
                from { transform: translateX(0); opacity: 1; }
                to { transform: translateX(100%); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
        
        document.body.appendChild(toast);
        
        // 3秒后自动移除
        setTimeout(() => {
            toast.style.animation = 'slideOut 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    // 拖拽上传功能
    const dropZone = document.querySelector('.card-body');
    
    dropZone.addEventListener('dragover', function(e) {
        e.preventDefault();
        dropZone.style.backgroundColor = '#f8f9fa';
        dropZone.style.border = '2px dashed #0d6efd';
    });
    
    dropZone.addEventListener('dragleave', function(e) {
        e.preventDefault();
        dropZone.style.backgroundColor = '';
        dropZone.style.border = '';
    });
    
    dropZone.addEventListener('drop', function(e) {
        e.preventDefault();
        dropZone.style.backgroundColor = '';
        dropZone.style.border = '';
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            fileInput.files = files;
            fileInput.dispatchEvent(new Event('change'));
        }
    });

    // 语言切换快捷功能
    document.getElementById('srcLang').addEventListener('change', function() {
        const srcLang = this.value;
        const destLang = document.getElementById('destLang').value;
        
        // 如果源语言和目标语言相同，自动切换目标语言
        if (srcLang === destLang) {
            const destSelect = document.getElementById('destLang');
            if (srcLang === 'eng') {
                destSelect.value = 'chi_sim';
            } else {
                destSelect.value = 'eng';
            }
        }
    });

    // 键盘快捷键
    document.addEventListener('keydown', function(e) {
        // Ctrl+Enter 或 Cmd+Enter 提交表单
        if ((e.ctrlKey || e.metaKey) && e.key === 'Enter') {
            if (fileInput.files[0] && !translateBtn.disabled) {
                uploadForm.dispatchEvent(new Event('submit'));
            }
        }
    });
});
