#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
ImageTranslator Web Application
基于 Flask 的图片翻译 Web 界面
"""

import os
import io
import base64
from flask import Flask, render_template, request, jsonify, send_file
from werkzeug.utils import secure_filename
import cv2
import numpy as np
from PIL import Image
import logging

# 导入 ImageTranslator 核心功能
import sys
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 由于兼容性问题，我们需要创建一个简化版本的 ImageTranslator
try:
    from image_translator.utils import lang
    # 暂时跳过完整的 ImageTranslator 导入，因为有依赖问题
    # from image_translator.image_translator import ImageTranslator
except ImportError as e:
    logger.error(f"导入模块失败: {e}")
    # 创建一个简化的语言映射
    class MockLang:
        TRANS_LANG = {
            'eng': ['en', 'en', 'en'],
            'chi_sim': ['zh-cn', 'zh-Hans', 'zh'],
            'chi_tra': ['zh-tw', 'zh-Hant', 'invalid'],
            'fra': ['fr', 'fr', 'fr'],
            'deu': ['de', 'de', 'de'],
            'spa': ['es', 'es', 'invalid'],
            'ita': ['it', 'it', 'invalid'],
            'por': ['pt', 'pt', 'invalid'],
            'rus': ['ru', 'ru', 'invalid'],
            'jpn': ['ja', 'ja', 'invalid'],
            'kor': ['ko', 'ko', 'invalid'],
        }
    lang = MockLang()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# 配置上传文件夹
UPLOAD_FOLDER = 'uploads'
RESULT_FOLDER = 'results'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}

# 创建必要的文件夹
for folder in [UPLOAD_FOLDER, RESULT_FOLDER]:
    if not os.path.exists(folder):
        os.makedirs(folder)

def allowed_file(filename):
    """检查文件扩展名是否允许"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def get_language_options():
    """获取支持的语言选项"""
    languages = {}
    for lang_code, trans_codes in lang.TRANS_LANG.items():
        # 只显示至少有一个翻译器支持的语言
        if any(code != 'invalid' for code in trans_codes):
            # 简化语言名称显示
            lang_names = {
                'eng': '英语', 'fra': '法语', 'deu': '德语', 'spa': '西班牙语',
                'ita': '意大利语', 'por': '葡萄牙语', 'rus': '俄语', 'jpn': '日语',
                'kor': '韩语', 'chi_sim': '中文(简体)', 'chi_tra': '中文(繁体)',
                'ara': '阿拉伯语', 'hin': '印地语', 'tha': '泰语', 'vie': '越南语'
            }
            display_name = lang_names.get(lang_code, lang_code.upper())
            languages[lang_code] = display_name
    return languages

def get_translator_options():
    """获取翻译器选项"""
    return {
        'google': 'Google 翻译',
        'bing': 'Bing 翻译',
        'deepl': 'DeepL 翻译'
    }

def get_ocr_options():
    """获取 OCR 选项"""
    return {
        'tesseract': 'Tesseract OCR',
        'easyocr': 'EasyOCR'
    }

@app.route('/')
def index():
    """主页"""
    return render_template('index.html',
                         languages=get_language_options(),
                         translators=get_translator_options(),
                         ocr_options=get_ocr_options())

@app.route('/upload', methods=['POST'])
def upload_file():
    """处理文件上传和翻译"""
    try:
        # 检查是否有文件上传
        if 'file' not in request.files:
            return jsonify({'error': '没有选择文件'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': '没有选择文件'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': '不支持的文件格式'}), 400
        
        # 获取表单参数
        src_lang = request.form.get('src_lang', 'eng')
        dest_lang = request.form.get('dest_lang', 'chi_sim')
        translator = request.form.get('translator', 'google')
        ocr = request.form.get('ocr', 'tesseract')
        
        # 验证参数
        if src_lang not in lang.TRANS_LANG:
            return jsonify({'error': '不支持的源语言'}), 400
        if dest_lang not in lang.TRANS_LANG:
            return jsonify({'error': '不支持的目标语言'}), 400
        
        # 读取图片数据
        file_data = file.read()
        
        # 将图片转换为 numpy 数组
        nparr = np.frombuffer(file_data, np.uint8)
        img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
        img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
        
        # 使用简化版翻译器
        from simple_translator import create_simple_translator

        # 语言代码映射
        lang_mapping = {
            'eng': 'en',
            'chi_sim': 'zh-cn',
            'chi_tra': 'zh-tw',
            'fra': 'fr',
            'deu': 'de',
            'spa': 'es',
            'ita': 'it',
            'por': 'pt',
            'rus': 'ru',
            'jpn': 'ja',
            'kor': 'ko'
        }

        src_lang_code = lang_mapping.get(src_lang, 'en')
        dest_lang_code = lang_mapping.get(dest_lang, 'zh-cn')

        # 创建简化版翻译器实例
        logger.info("开始处理图片翻译...")
        simple_translator = create_simple_translator(img, src_lang_code, dest_lang_code)

        # 执行翻译
        result = simple_translator.process()

        if not result['success']:
            return jsonify({'error': result['error']}), 500

        result_img = result['result_image']
        text_info = result['text_info']

        # 将结果图片转换为 base64 编码
        try:
            # 确保图片数据类型正确
            if result_img.dtype != np.uint8:
                result_img = result_img.astype(np.uint8)

            # 转换颜色空间
            result_img_bgr = cv2.cvtColor(result_img, cv2.COLOR_RGB2BGR)

            # 编码为PNG格式
            success, buffer = cv2.imencode('.png', result_img_bgr)
            if not success:
                raise Exception("图片编码失败")

            # 转换为base64
            img_base64 = base64.b64encode(buffer).decode('utf-8')
            logger.info(f"图片编码成功，大小: {len(img_base64)} 字符")

        except Exception as e:
            logger.error(f"图片编码失败: {e}")
            # 如果编码失败，返回错误
            return jsonify({'error': f'图片处理失败: {str(e)}'}), 500
        
        return jsonify({
            'success': True,
            'result_image': img_base64,
            'text_info': text_info,
            'parameters': {
                'src_lang': src_lang,
                'dest_lang': dest_lang,
                'translator': translator,
                'ocr': ocr
            }
        })
        
    except Exception as e:
        logger.error(f"翻译过程中发生错误: {str(e)}")
        return jsonify({'error': f'翻译失败: {str(e)}'}), 500

@app.route('/health')
def health_check():
    """健康检查接口"""
    return jsonify({'status': 'ok', 'message': 'ImageTranslator Web App is running'})

if __name__ == '__main__':
    print("启动 ImageTranslator Web 应用程序...")
    print("访问地址: http://localhost:5000")
    app.run(debug=True, host='0.0.0.0', port=5000)
