#  Copyright (C) 2020  A2va

# ImageTranslator is free software: you can redistribute it and/or modify
# it under the terms of the GNU General Public License as published by
# the Free Software Foundation, either version 3 of the License, or
# (at your option) any later version.

# ImageTranslator is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY; without even the implied warranty of
# MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
# GNU General Public License for more details.

# You should have received a copy of the GNU General Public License
# along with ImageTranslator. If not, see <https://www.gnu.org/licenses/>.

# For language code: https://iso639-3.sil.org/

TRANS_LANG = {
    # Google Trans # Bing # DeepL
    'afr': ['af', 'af', 'invalid'],               # Afrikaans
    'amh': ['am', 'invalid', 'invalid'],          # Amharic
    'ara': ['ar', 'ar', 'invalid'],               # Arabic
    'aze': ['az', 'invalid', 'invalid'],          # Azerbaijani

    'bel': ['be', 'invalid', 'invalid'],  # Belarusian
    'ben': ['bn', 'bn-BD', 'invalid'],  # Bengali
    'bos': ['bs', 'bs-latn', 'invalid'],  # Bosnian
    'bul': ['bg', 'bg', 'invalid'],  # Bulgarian

    'cat': ['ca', 'ca', 'invalid'],  # Catalan
    'ceb': ['ceb', 'invalid', 'invalid'],  # Cebuano
    'ces': ['cs', 'cs', 'invalid'],  # Czech
    'chi_sim': ['zh-cn', 'zh-Hans', 'zh'],  # Chinese (simplified)
    'chi_tra': ['zh-tw', 'zh-Hant', 'invalid'],  # Chinese (traditional)
    'cos': ['co', 'invalid', 'invalid'],  # Corsican
    'cym': ['cy', 'cy', 'invalid'],  # Welsh

    'dan': ['da', 'da', 'invalid'],  # Danish
    'deu': ['de', 'de', 'de'],  # German

    'ell': ['el', 'el', 'invalid'],  # Greek
    'eng': ['en', 'en', 'en'],  # English
    'est': ['et', 'et', 'invalid'],  # Estonian
    'eus': ['eu', 'invalid', 'invalid'],  # Basque

    'fas': ['fa', 'fa', 'invalid'],  # Persian
    'fin': ['fi', 'fi', 'invalid'],  # Finnish
    'fra': ['fr', 'fr', 'fr'],  # French
    'fry': ['fy', 'invalid', 'invalid'],  # Frisian

    'gla': ['gd', 'invalid', 'invalid'],  # Scottish Gaelic
    'gle': ['ga', 'invalid', 'invalid'],  # Irish
    'glg': ['gl', 'invalid', 'invalid'],  # Galician
    'guj': ['gu', 'invalid', 'invalid'],  # Gujarati

    'hat': ['ht', 'invalid', 'invalid'],  # Haitian
    'heb': ['he', 'he', 'invalid'],  # Hebrew
    'hin': ['hi', 'hi', 'invalid'],  # Hindi
    'hrv': ['hr', 'hr', 'invalid'],  # Croatian
    'hun': ['hu', 'hu', 'invalid'],  # Hungarian
    'hye': ['hy', 'invalid', 'invalid'],  # Armenian

    'ind': ['id', 'id', 'invalid'],  # Indonesian
    'isl': ['is', 'is', 'invalid'],  # Icelandic
    'ita': ['it', 'it', 'it'],  # Italian

    'jav': ['jw', 'invalid', 'invalid'],  # Javanese
    'jpn': ['ja', 'ja', 'ja'],  # Japanese

    'kan': ['ka', 'invalid', 'invalid'],  # Kannada
    'kat': ['ka', 'invalid', 'invalid'],  # Georgian
    'kaz': ['kk', 'invalid', 'invalid'],  # Kazakh
    'khm': ['km', 'invalid', 'invalid'],  # Khmer
    'kir': ['ky', 'invalid', 'invalid'],  # Kyrgyz
    'kmr': ['ku', 'invalid', 'invalid'],  # Kurdish
    'kor': ['ko', 'ko', 'invalid'],  # Korean

    'lao': ['lo', 'invalid', 'invalid'],  # Lao
    'lat': ['la', 'invalid', 'invalid'],  # Latin
    'lav': ['lv', 'lv', 'invalid'],  # Latvian
    'lit': ['lt', 'lt', 'invalid'],  # Lithuanian
    'ltz': ['lb', 'invalid', 'invalid'],  # Luxembourgish

    'mal': ['ml', 'invalid', 'invalid'],  # Malayalam
    'mar': ['mr', 'invalid', 'invalid'],  # Marathi
    'mkd': ['mk', 'invalid', 'invalid'],  # Macedonian
    'mlt': ['mt', 'mt', 'invalid'],  # Maltese
    'mon': ['mn', 'invalid', 'invalid'],  # Mongolian
    'mri': ['mi', 'invalid', 'invalid'],  # Maori
    'msa': ['ms', 'ms', 'invalid'],  # Malay
    'mya': ['my', 'invalid', 'invalid'],  # Burmese

    'nep': ['ne', 'invalid', 'invalid'],  # Nepali
    'nld': ['nl', 'nl', 'nl'],  # Dutch
    'nor': ['no', 'no', 'invalid'],  # Norwegian

    'ory': ['or', 'invalid', 'invalid'],  # Odiya

    'pol': ['pl', 'pl', 'pl'],  # Polish
    'por': ['pt', 'pt', 'pt'],  # Portuguese
    'pus': ['ps', 'invalid', 'invalid'],  # Pashto

    'ron': ['ro', 'ro', 'invalid'],  # Romanian
    'rus': ['ru', 'ru', 'ru'],  # Russian

    'slk': ['sk', 'sk', 'invalid'],  # Slovak
    'slv': ['sl', 'sl', 'invalid'],  # Slovenian
    'snd': ['sd', 'invalid', 'invalid'],  # Sindhi
    'spa': ['es', 'es', 'es'],  # Spanish
    'sqi': ['sq', 'invalid', 'invalid'],  # Albanian
    'srp_latn': ['sr', 'sr-Latn', 'invalid'],  # Serbian
    'srp': ['invalid', 'sr-Cyrl', 'invalid'],  # Serbian (cyrillic)
    'sun': ['su', 'invalid', 'invalid'],  # Sundanese
    'swa': ['sw', 'invalid', 'invalid'],  # Swahili
    'swe': ['sv', 'sv', 'invalid'],  # Swedish
    'sna': ['sn', 'invalid', 'invalid'],  # Shona
    'sin': ['si', 'invalid', 'invalid'],  # Sinhala

    'tam': ['ta', 'ta', 'invalid'],  # Tamil
    'tel': ['te', 'te', 'invalid'],  # Telegu
    'tgk': ['tg', 'invalid', 'invalid'],  # Tajiik
    'tha': ['th', 'th', 'invalid'],  # Thai
    'ton': ['invalid', 'to', 'invalid'],  # Tongan
    'tur': ['tr', 'tr', 'invalid'],  # Turkish

    'uig': ['ug', 'invalid', 'invalid'],  # Uighur
    'ukr': ['uk', 'uk', 'invalid'],  # Ukrainian
    'urd': ['ur', 'ur', 'invalid'],  # Urdu
    'uzb': ['uz', 'invalid', 'invalid'],  # Uzbek
    'vie': ['vi', 'vi', 'invalid'],  # Vietnamese

    'yid': ['yi', 'invalid', 'invalid'],  # Yiddish
    'yor': ['yo', 'invalid', 'invalid']  # Yoruba

    # 'zul': ['zu'],                             #Zulu
    # 'xho': ['xh'],                             #Xhosa
    # 'som': ['sl'],                             #Somali
    # 'st':['st'],                               #Sesotho
    # 'pan': [''],                               #Punjabi
    # 'nya': ['ny'],                             #Chichewa
    # 'mlg': ['mg','mg'],                        #Malagasy
    # 'ibo': ['ib'],                             #Igbo
    # 'hmn': ['hmn',mww'],                       #Hmong
    # 'mww': ['invalid','mww'],                  #Hmong Daw
    # 'epo': ['eo'],                             #Esperanto
    # 'haw': ['hw'],                             #Hawaiian
    # 'fil': ['tl'],                             #Filipino
    # 'hau': ['ha'],                             #Hausa
    # 'smo': ['sm','sm'],                        #Samoan
    # 'fij': ['invalid','fj'],                   #Fijian
    # 'otq': ['invalid','otq'],                  #Querétaro Otomi
    # 'swh': ['invalid','sw'],                   #Kiswahili
    # 'tlh': ['invalid','tlh'],                  #Klingon
    # 'tah': ['invalid','ty'],                   #Tahitian
    # 'yua': ['invalid','yua'],                  #Yucatec Maya
    # 'yue': ['invalid','yue']                   #Cantonese (Traditional)
}


OCR_LANG = {
    # Tesseract #EasyOCR
    # 'abq':['invalid','abq'],                   #Abaza
    # 'ady':['invalid','ady'],                   #Adyghe
    # 'anp':['invalid','ang'],                   #Angika
    'afr': ['afr', 'af'],  # Afrikaans
    'amh': ['amh', 'invalid'],  # Amharic
    'ara': ['ara', 'ar'],  # Arabic
    # 'asm': ['asm','as'],                       #Assamese
    # 'ava':['invalid','ava'],                   #Avar
    'aze': ['aze', 'az'],  # Azerbaijani
    # 'aze_cyrl': ['aze_cyrl','invalid'],        #Azerbaijani (cyrillic)
    # 'bih': ['invalid','bh'],                   #Bihari
    'bel': ['bel', 'be'],  # Belarusian
    'ben': ['ben', 'bn'],  # Bengali
    # 'bho': ['invalid','bho'],                  #Bhojpuri
    # 'bod': ['bod','invalid'],                  #Tibetan
    'bos': ['invalid', 'bos'],  # Bosnian
    'bul': ['bul', 'bg'],  # Bulgarian
    'cat': ['cat', 'invalid'],  # Catalan
    'ceb': ['ceb', 'invalid'],  # Cebuano
    'ces': ['ces', 'cz'],  # Czech
    # 'che': ['invalid','che'],                  #Chechen
    'chi_sim': ['chi_sim', 'ch_sim'],  # Chinese (simplified)
    # Chinese vertical (simplified)
    'chi_sim_vert': ['chi_sim_vert', 'invalid'],
    'chi_tra': ['chi_tra', 'ch_tra'],  # Chinese (traditional)
    # Chinese vertical (traditional)
    'chi_tra_vert': ['chi_tra_vert', 'invalid'],
    # 'chr': ['chr','invalid'],                  #Cherokee
    'cos': ['cos', 'invalid'],  # Corsican
    'cym': ['invalid', 'cy'],  # Welsh
    # 'sim': ['sim','invalid'],                  #Mende (Papua New Guinea)
    'dan': ['dan', 'da'],  # Danish
    # 'dar': ['invalid','dar'],                  #Dargwa
    'deu': ['deu', 'de'],  # German
    # 'div': ['div','invalid'],                  #Divehi
    # 'dzo': ['dzo','invalid'],                  #Dzongkha
    'ell': ['ell', 'invalid'],  # Greek (modern)
    'eng': ['eng', 'en'],  # English
    # 'enm': ['enm','invalid'],                  #English (middle)
    'est': ['est', 'et'],  # Estonian
    'eus': ['eus', 'invalid'],  # Basque
    # 'fao': ['fao','invalid'],                  #Faroese
    'fas': ['fas', 'fa'],  # Persian
    'fin': ['fin', 'invalid'],  # Finnish
    'fra': ['fra', 'fr'],  # French
    'frk': ['frk', 'invalid'],  # Frankish
    # 'frm': ['frm','invalid'],                  #French (middle)
    'fry': ['fry', 'invalid'],  # Frisian (western)
    'gla': ['gla', 'invalid'],  # Scottish Gaelic
    'gle': ['gle', 'ga'],  # Irish
    'glg': ['glg', 'invalid'],  # Galician
    # 'gom': ['invalid','gom'],                  #Goan Konkani
    # 'grc': ['grc','invalid'],                  #Greek (ancient)
    # 'quj': ['quj','invalid'],                  #Gujuarati
    'hat': ['hat', 'invalid'],  # Haitian
    'heb': ['heb', 'invalid'],  # Hebrew
    'hin': ['hin', 'hi'],  # Hindi
    'hrv': ['hrv', 'hr'],  # Croatian
    'hun': ['hun', 'hu'],  # Hungarian
    'hye': ['hye', 'invalid'],  # Armenian
    # 'iku': ['iku'.'invalid'],                  #Inuktitut
    'ind': ['ind', 'id'],  # Indonesian
    # 'inh': ['invalid','inh'],                  #Ingush
    'isl': ['isl', 'is'],  # Icelandic
    'ita': ['ita', 'it'],  # Italian
    # 'ita_old': ['ita_old','invalid'],          #Italian (old)
    'jav': ['jav', 'invalid'],  # Javanese
    'jpn': ['jpn', 'ja'],  # Japanese
    'jpn_vert': ['jpn_vert', 'invalid'],  # Japanese (vertical)
    'kan': ['kan', 'invalid'],  # Kannada
    'kat': ['kat', 'invalid'],  # Georgian
    # 'kat_old': ['kat_old','invalid'],          #Georgian (old)
    'kaz': ['kaz', 'invalid'],  # Kazakh
    # 'kdb': ['invalid','kdb'],                  #Kabardian
    'khm': ['khm', 'invalid'],  # Khmer
    # 'kir': ['kir','invalid'],                  #Kirghiz
    'kmr': ['kmr', 'ku'],  # Kurdish (northerm)
    'kor': ['kor', 'ko'],  # Korean
    'kor_vert': ['kor_vert', 'invalid'],  # Korean (vertical)
    'lao': ['lao', 'invalid'],  # Lao
    'lat': ['lat', 'la'],  # Latin
    'lav': ['lav', 'lv'],  # Latvian
    # 'lbe': ['invalid','lbe'],                  #Lak
    # 'lez': ['invalid','lez'],                  #Lezghian
    'lit': ['lit', 'lt'],  # Lithuanian
    'ltz': ['ltz', 'invalid'],  # Luxembourgish
    # 'mai': ['invalid','mai'],                  #Maithili
    # 'mah': ['invalid','mah'],                  #Magahi
    'mal': ['mal', 'invalid'],  # Malayalam
    'mar': ['mar', 'mr'],  # Marathi
    'mkd': ['mkd', 'invalid'],  # Macedonian
    'mlt': ['mlt', 'mt'],  # Maltese
    'mon': ['mon', 'mn'],  # Mongolian
    'mri': ['mri', 'mi'],  # Maori
    'msa': ['msa', 'ms'],  # Malay
    'mya': ['mya', 'invalid'],  # Burmese
    'nep': ['nep', 'ne'],  # Nepali
    'new': ['invalid', 'new'],  # Newari
    'nld': ['nld', 'nl'],  # Dutch
    'nor': ['nor', 'no'],  # Norwegian
    # 'oci': ['oci','oc'],                       #Occitan
    'ori': ['ori', 'invalid'],  # Oriya
    # 'pan': ['pan','invalid'],                  #Panjabi
    'pol': ['pol', 'pl'],  # Polish
    'por': ['por', 'pt'],  # Portuguese
    'pus': ['pus', 'invalid'],  # Pushto
    # 'que': ['que','invalid'],                  #Quechua
    'ron': ['ron', 'ro'],  # Romanian
    'rus': ['rus', 'ru'],  # Russian
    # 'san': ['san','invalid'],                  #Sanskrit
    # 'sck': ['invalid','sck'],                  #Nagpuri
    'sin': ['sin', 'invalid'],  # Sinhala
    'slk': ['slk', 'sk'],  # Slovak
    'slv': ['slv', 'sl'],  # Slovenian
    'snd': ['snd', 'invalid'],  # Sindhi
    'spa': ['spa', 'es'],  # Spanish
    # 'spa_old': ['spa_old'],                    #Spanish (old)
    'sqi': ['sqi', 'sq'],  # Albanian
    'srp': ['srp', 'rs_cyrillic'],  # Serbian (cyrillic)
    'srp_latn': ['srp_latn', 'rs_latin'],  # Serbian (latin)
    'sun': ['sun', 'invalid'],  # Sundanese
    'swa': ['swa', 'sw'],  # Swahili
    'swe': ['swe', 'sv'],  # Swedish
    # 'syr': ['syr','invalid'],                  #Syriac
    # 'tab': ['invalid','tab'],                  #Tabassaran
    'tam': ['tam', 'ta'],  # Tamil
    'tat': ['tat', 'invalid'],  # Tatar
    'tel': ['tgk', 'invalid'],  # Telugu
    'tgk': ['tgk', 'invalid'],  # Tajiik
    'tha': ['tha', 'th'],  # Thai
    # 'tir': ['tir','invalid'],                  #Tigrinya
    # 'tl': ['invalid','tl'],                    #Tagalog
    'ton': ['ton', 'invalid'],  # Tonga
    'tur': ['tur', 'tr'],  # Turkish
    'uig': ['uig', 'ug'],  # Uighur
    'ukr': ['ukr', 'uk'],  # Ukrainian
    'urd': ['urd', 'ur'],  # Urdu
    'uzb': ['uzb', 'uz'],  # Uzbek
    'urb_cyrl': ['urb_cyrl', 'invalid'],  # Uzbek (cyrillic)
    'vie': ['vie', 'vi'],  # Vietnamese
    'yid': ['yid', 'invalid'],  # Yiddish
    'yor': ['yor', 'invalid'],  # Yoruba
}
