/* 图片翻译器自定义样式 */

body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
}

/* 卡片样式 */
.card {
    border: none;
    border-radius: 15px;
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15) !important;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border-bottom: 2px solid #e9ecef;
}

/* 文件上传区域 */
.form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 图片预览样式 */
#imagePreview {
    text-align: center;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 10px;
    border: 2px dashed #dee2e6;
}

#previewImg {
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}

/* 按钮样式 */
.btn {
    border-radius: 10px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(45deg, #0d6efd, #6610f2);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(45deg, #0b5ed7, #5a0fc8);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(13, 110, 253, 0.4);
}

/* 加载动画 */
.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* 结果区域样式 */
#resultImage {
    max-height: 600px;
    object-fit: contain;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* 文本列表样式 */
.list-group-item {
    border-radius: 10px !important;
    margin-bottom: 10px;
    border: 1px solid #e9ecef;
    transition: all 0.2s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.text-original {
    background-color: #fff3cd;
    padding: 8px 12px;
    border-radius: 8px;
    margin-bottom: 8px;
    border-left: 4px solid #ffc107;
}

.text-translated {
    background-color: #d1ecf1;
    padding: 8px 12px;
    border-radius: 8px;
    border-left: 4px solid #17a2b8;
}

/* 欢迎区域样式 */
#welcomeArea {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 15px;
    margin: 20px 0;
}

#welcomeArea .text-muted {
    color: rgba(255,255,255,0.8) !important;
}

#welcomeArea .fa-2x {
    margin-bottom: 10px;
}

/* 错误信息样式 */
.alert {
    border-radius: 15px;
    border: none;
}

.alert-danger {
    background: linear-gradient(45deg, #dc3545, #c82333);
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card {
        margin-bottom: 20px;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    #resultImage {
        max-height: 400px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    background: linear-gradient(45deg, #28a745, #20c997);
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 选择框样式 */
.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 图标样式 */
.fa-upload, .fa-magic, .fa-check-circle, .fa-exclamation-triangle {
    color: inherit;
}

/* 特殊效果 */
.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0,0,0,0.175) !important;
}

/* 文本样式 */
.text-gradient {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 加载状态样式 */
#loadingArea {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border-radius: 15px;
    margin: 20px 0;
}

#loadingArea .spinner-border {
    border-color: rgba(255,255,255,0.3);
    border-top-color: white;
}
