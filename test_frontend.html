<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端图片显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-section h3 {
            color: #333;
            margin-top: 0;
        }
        .image-container {
            text-align: center;
            margin: 20px 0;
        }
        .test-image {
            max-width: 100%;
            height: auto;
            border: 2px solid #ddd;
            border-radius: 8px;
            margin: 10px;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #uploadForm {
            border: 2px dashed #ccc;
            padding: 20px;
            text-align: center;
            border-radius: 8px;
        }
        #fileInput {
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 图片翻译前端测试页面</h1>
        
        <!-- 测试1: 显示本地生成的图片 -->
        <div class="test-section">
            <h3>测试1: 本地生成的图片显示</h3>
            <div class="image-container">
                <div>
                    <h4>原始测试图片</h4>
                    <img src="test_image.png" alt="原始测试图片" class="test-image" 
                         onload="showStatus('img1-status', '✅ 原始图片加载成功', 'success')"
                         onerror="showStatus('img1-status', '❌ 原始图片加载失败', 'error')">
                    <div id="img1-status" class="status info">加载中...</div>
                </div>
                
                <div>
                    <h4>翻译结果图片</h4>
                    <img src="test_result.png" alt="翻译结果图片" class="test-image"
                         onload="showStatus('img2-status', '✅ 结果图片加载成功', 'success')"
                         onerror="showStatus('img2-status', '❌ 结果图片加载失败', 'error')">
                    <div id="img2-status" class="status info">加载中...</div>
                </div>
                
                <div>
                    <h4>Web测试结果图片</h4>
                    <img src="web_test_result.png" alt="Web测试结果图片" class="test-image"
                         onload="showStatus('img3-status', '✅ Web结果图片加载成功', 'success')"
                         onerror="showStatus('img3-status', '❌ Web结果图片加载失败', 'error')">
                    <div id="img3-status" class="status info">加载中...</div>
                </div>
            </div>
        </div>
        
        <!-- 测试2: Web API 测试 -->
        <div class="test-section">
            <h3>测试2: Web API 上传测试</h3>
            <form id="uploadForm">
                <div>
                    <label for="fileInput">选择图片文件:</label>
                    <input type="file" id="fileInput" accept="image/*" required>
                </div>
                <div>
                    <button type="submit">上传并翻译</button>
                    <button type="button" onclick="testWithLocalImage()">使用本地测试图片</button>
                </div>
            </form>
            
            <div id="uploadStatus" class="status info" style="display: none;">处理中...</div>
            
            <div id="resultContainer" style="display: none;">
                <h4>翻译结果:</h4>
                <div class="image-container">
                    <img id="resultImage" alt="翻译结果" class="test-image">
                </div>
                <div id="textResults"></div>
            </div>
        </div>
        
        <!-- 测试3: Base64 图片测试 -->
        <div class="test-section">
            <h3>测试3: Base64 图片编码测试</h3>
            <button onclick="testBase64()">测试 Base64 编码</button>
            <div id="base64Status" class="status info" style="display: none;">测试中...</div>
            <div id="base64Result" style="display: none;">
                <h4>Base64 编码结果:</h4>
                <div class="image-container">
                    <img id="base64Image" alt="Base64图片" class="test-image">
                </div>
            </div>
        </div>
    </div>

    <script>
        function showStatus(elementId, message, type) {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `status ${type}`;
        }

        // 上传表单处理
        document.getElementById('uploadForm').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];
            
            if (!file) {
                showStatus('uploadStatus', '❌ 请选择文件', 'error');
                document.getElementById('uploadStatus').style.display = 'block';
                return;
            }
            
            uploadFile(file);
        });

        function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('src_lang', 'eng');
            formData.append('dest_lang', 'chi_sim');
            formData.append('translator', 'google');
            formData.append('ocr', 'tesseract');
            
            document.getElementById('uploadStatus').style.display = 'block';
            showStatus('uploadStatus', '🔄 上传中...', 'info');
            
            fetch('/upload', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showStatus('uploadStatus', '✅ 翻译成功!', 'success');
                    showResult(data);
                } else {
                    showStatus('uploadStatus', `❌ 翻译失败: ${data.error}`, 'error');
                }
            })
            .catch(error => {
                showStatus('uploadStatus', `❌ 网络错误: ${error.message}`, 'error');
            });
        }

        function showResult(data) {
            const resultContainer = document.getElementById('resultContainer');
            const resultImage = document.getElementById('resultImage');
            const textResults = document.getElementById('textResults');
            
            // 显示图片
            if (data.result_image) {
                resultImage.src = 'data:image/png;base64,' + data.result_image;
                resultImage.onload = function() {
                    console.log('✅ 结果图片显示成功');
                };
                resultImage.onerror = function() {
                    console.error('❌ 结果图片显示失败');
                };
            }
            
            // 显示文本结果
            let textHtml = '<h5>识别的文本:</h5>';
            data.text_info.forEach((item, index) => {
                textHtml += `
                    <div style="margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px;">
                        <strong>文本 ${index + 1}:</strong><br>
                        <span style="background: #fff3cd; padding: 2px 5px;">原文: ${item.original}</span><br>
                        <span style="background: #d1ecf1; padding: 2px 5px;">译文: ${item.translated}</span>
                    </div>
                `;
            });
            textResults.innerHTML = textHtml;
            
            resultContainer.style.display = 'block';
        }

        function testWithLocalImage() {
            fetch('test_image.png')
                .then(response => response.blob())
                .then(blob => {
                    const file = new File([blob], 'test_image.png', { type: 'image/png' });
                    uploadFile(file);
                })
                .catch(error => {
                    showStatus('uploadStatus', `❌ 无法加载本地图片: ${error.message}`, 'error');
                    document.getElementById('uploadStatus').style.display = 'block';
                });
        }

        function testBase64() {
            document.getElementById('base64Status').style.display = 'block';
            showStatus('base64Status', '🔄 测试中...', 'info');
            
            fetch('web_test_result.png')
                .then(response => response.blob())
                .then(blob => {
                    const reader = new FileReader();
                    reader.onload = function() {
                        const base64 = reader.result.split(',')[1];
                        document.getElementById('base64Image').src = 'data:image/png;base64,' + base64;
                        document.getElementById('base64Result').style.display = 'block';
                        showStatus('base64Status', '✅ Base64 测试成功', 'success');
                    };
                    reader.readAsDataURL(blob);
                })
                .catch(error => {
                    showStatus('base64Status', `❌ Base64 测试失败: ${error.message}`, 'error');
                });
        }
    </script>
</body>
</html>
