#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
创建测试图片
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_test_image():
    """创建一个包含中英文文字的测试图片"""
    
    # 创建画布
    width, height = 800, 600
    img = Image.new('RGB', (width, height), color='white')
    draw = ImageDraw.Draw(img)
    
    # 尝试加载字体
    try:
        # 尝试使用系统字体
        font_large = ImageFont.truetype('arial.ttf', size=40)
        font_medium = ImageFont.truetype('arial.ttf', size=30)
        font_small = ImageFont.truetype('arial.ttf', size=20)
    except:
        # 使用默认字体
        font_large = ImageFont.load_default()
        font_medium = ImageFont.load_default()
        font_small = ImageFont.load_default()
    
    # 绘制标题
    draw.text((50, 50), "Image Translation Test", fill='black', font=font_large)
    draw.text((50, 100), "图片翻译测试", fill='blue', font=font_large)
    
    # 绘制一些英文文本
    texts = [
        "Waterproof Design",
        "Anti-drop Protection", 
        "High Temperature Resistant",
        "Outdoor Special Use",
        "Normal Lighting Function",
        "Pure Copper Material"
    ]
    
    y_pos = 180
    for text in texts:
        draw.text((50, y_pos), text, fill='black', font=font_medium)
        y_pos += 50
    
    # 绘制一些中文文本
    chinese_texts = [
        "防水设计",
        "防摔保护",
        "耐高温材料"
    ]
    
    y_pos = 180
    for text in chinese_texts:
        draw.text((400, y_pos), text, fill='red', font=font_medium)
        y_pos += 50
    
    # 保存图片
    img.save('test_image.png')
    print("测试图片已创建: test_image.png")

if __name__ == "__main__":
    create_test_image()
