#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试Web应用程序的上传功能
"""

import requests
import base64
import json

def test_web_upload():
    """测试Web应用程序的图片上传功能"""
    
    url = 'http://localhost:5000/upload'
    
    try:
        # 读取测试图片
        with open('test_image.png', 'rb') as f:
            files = {'file': ('test_image.png', f, 'image/png')}
            data = {
                'src_lang': 'eng',
                'dest_lang': 'chi_sim',
                'translator': 'google',
                'ocr': 'tesseract'
            }
            
            print("发送请求到Web应用程序...")
            response = requests.post(url, files=files, data=data, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print("✅ Web上传测试成功！")
                    print(f"检测到 {len(result['text_info'])} 个文本区域")
                    
                    # 检查图片数据
                    if 'result_image' in result:
                        img_data = result['result_image']
                        print(f"图片数据长度: {len(img_data)} 字符")
                        
                        # 尝试解码图片数据
                        try:
                            img_bytes = base64.b64decode(img_data)
                            print(f"解码后图片大小: {len(img_bytes)} 字节")
                            
                            # 保存解码后的图片
                            with open('web_test_result.png', 'wb') as f:
                                f.write(img_bytes)
                            print("Web测试结果图片已保存为: web_test_result.png")
                            
                        except Exception as e:
                            print(f"❌ 图片解码失败: {e}")
                    
                    # 显示文本信息
                    for i, text_info in enumerate(result['text_info']):
                        print(f"文本 {i+1}:")
                        print(f"  原文: {text_info['original']}")
                        print(f"  译文: {text_info['translated']}")
                        print()
                        
                else:
                    print(f"❌ 翻译失败: {result.get('error', '未知错误')}")
            else:
                print(f"❌ HTTP请求失败: {response.status_code}")
                print(f"响应内容: {response.text}")
                
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到Web应用程序，请确保服务器正在运行")
    except Exception as e:
        print(f"❌ 测试过程中发生错误: {e}")

if __name__ == "__main__":
    test_web_upload()
